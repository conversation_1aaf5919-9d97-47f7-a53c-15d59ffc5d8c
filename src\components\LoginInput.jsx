import React from 'react';
import PropTypes from 'prop-types';

class LoginInput extends React.Component {
    constructor(props){
        super(props);

        this.state = {
            email: '',
            password: '',
        };

        this.onEmailChangeHandler = this.onEmailChangeHandler.bind(this);
        this.onPasswordChangeHandler = this.onPasswordChangeHandler.bind(this);
        this.onSubmitHandler = this.onSubmitHandler.bind(this);
    }

    onEmailChangeHandler(event){
        this.setState(()=>{
            return {
                email: event.target.value,
            };
        });
    }

    onPasswordChangeHandler(event){
        this.setState(()=>{
            return {
                password: event.target.value,
            };
        });
    }

    onSubmitHandler(event){
        event.preventDefault();

        this.props.login({
            email: this.state.email,
            password: this.state.password,
        });
    }

    render(){
        return (
            <form onSubmit={this.onSubmitHandler} className='login-input' >
                <input type="text" placeholder="Email" value={this.state.email} onChange={this.onEmailChangeHand<PERSON>} />
                <input type="password" placeholder="Password" value={this.state.password} onChange={this.onPasswordChangeHandler} />
                <button type="submit" onClick={this.onSubmitHandler}>Login</button>
            </form>
        );
    }
}

LoginInput.propTypes = {
    login: PropTypes.func.isRequired,
}

export default LoginInput;