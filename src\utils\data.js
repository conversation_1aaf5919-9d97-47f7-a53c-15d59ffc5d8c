let contacts = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    tag: 'dimasmds',
    imageUrl: '/images/dimasmds.jpeg',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    tag: 'arifaizin',
    imageUrl: '/images/arifaizin.jpeg',
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    tag: 'rfajri27',
    imageUrl: '/images/rfajri27.jpeg',
  },
]
 
function getContacts() {
  return contacts;
}
 
function addContact(contact) {
  contacts = [...contacts, { id: +new Date(), imageUrl: '/images/default.jpg', ...contact }];
}
 
function deleteContact(id) {
  contacts = contacts.filter((contact) => contact.id !== id);
}
 
export { getContacts, addContact, deleteContact };
 